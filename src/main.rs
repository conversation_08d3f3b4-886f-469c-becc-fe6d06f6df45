use std::process::Command;
use std::io::{self, Write};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("DSK-CLI with ripgrep-all integration");

    // 示例1：调用 ripgrep-all 搜索文件
    call_ripgrep_all("search_pattern", ".")?;

    // 示例2：更复杂的调用
    call_ripgrep_all_advanced()?;

    Ok(())
}

/// 简单调用 ripgrep-all
fn call_ripgrep_all(pattern: &str, path: &str) -> Result<(), Box<dyn std::error::Error>> {
    println!("Searching for '{}' in '{}'", pattern, path);

    let output = Command::new("rga")  // ripgrep-all 的命令是 rga
        .arg(pattern)
        .arg(path)
        .output()?;

    if output.status.success() {
        println!("Search results:");
        io::stdout().write_all(&output.stdout)?;
    } else {
        eprintln!("Error running ripgrep-all:");
        io::stderr().write_all(&output.stderr)?;
    }

    Ok(())
}

/// 更高级的 ripgrep-all 调用示例
fn call_ripgrep_all_advanced() -> Result<(), Box<dyn std::error::Error>> {
    println!("\nAdvanced ripgrep-all usage:");

    let output = Command::new("rga")
        .arg("--type")
        .arg("pdf")  // 只搜索 PDF 文件
        .arg("--ignore-case")  // 忽略大小写
        .arg("search_term")
        .arg(".")
        .output()?;

    if output.status.success() {
        println!("PDF search results:");
        io::stdout().write_all(&output.stdout)?;
    } else {
        eprintln!("Error in advanced search:");
        io::stderr().write_all(&output.stderr)?;
    }

    Ok(())
}
